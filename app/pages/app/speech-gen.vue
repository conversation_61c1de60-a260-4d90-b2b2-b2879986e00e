<script setup lang="ts">
import { formatNumber } from '~/utils'
import {
  compareFileArrays,
  commonValidationRules
} from '~/utils/generationValidation'

const { authorize } = useAuthorize()
const route = useRoute()
const authStore = useAuthStore()
const { isAuthenticated, user_credit } = storeToRefs(authStore)
const {
  model,
  models,
  speed,
  outputFormat,
  outputChannel,
  speedConfig,
  outputFormats
} = useSpeechGenModels()
const { selectedVoice } = useSpeechVoices()
const { selectedEmotion } = useSpeechEmotions()
const router = useRouter()
const toast = useToast()
const { handleGeneration } = useGenerationConfirmation()
const { t } = useI18n()
const runtimeConfig = useRuntimeConfig()
const productStore = useProductStore()
const { getServicePriceByModelName } = storeToRefs(productStore)
const textToSpeechStore = useTextToSpeechStore()
const {
  textToSpeechResult,
  inputText,
  selectedFiles,
  supportFiles,
  hasSelectedFiles,
  uploadProgress,
  loadings,
  errors,
  supportFilesDisplay,
  custom_prompt,
  maxTextLength,
  inputTextLength
} = storeToRefs(textToSpeechStore)

// Create local ref for the component
const aiToolSpeechCardRef = ref(null)

// Store initial values to compare for changes
const initialValues = ref({
  inputText: '',
  model: null as any,
  selectedVoice: null as any,
  selectedEmotion: null as any,
  speed: 1.0,
  outputFormat: 'mp3',
  outputChannel: 'mono',
  selectedFiles: [] as File[]
})

// Speech notification handler
const handleSpeechNotification = (event: CustomEvent) => {
  const { historyDetail } = event.detail
  if (historyDetail) {
    textToSpeechStore.textToSpeechResult = historyDetail
  }
}

// Initialize initial values on mount
onMounted(() => {
  // Use nextTick to ensure all reactive values are properly initialized
  nextTick(() => {
    initialValues.value = {
      inputText: inputText.value,
      model: model.value,
      selectedVoice: selectedVoice.value,
      selectedEmotion: selectedEmotion.value,
      speed: speed.value,
      outputFormat: outputFormat.value,
      outputChannel: outputChannel.value,
      selectedFiles: [...selectedFiles.value]
    }

    // Set the ref in the store
    textToSpeechStore.aiToolSpeechCardRef = aiToolSpeechCardRef.value
  })

  // Listen for speech notification events
  window.addEventListener(
    'speechNotificationReceived',
    handleSpeechNotification as EventListener
  )
})

// Cleanup event listener on unmount
onUnmounted(() => {
  window.removeEventListener(
    'speechNotificationReceived',
    handleSpeechNotification as EventListener
  )
})

// Check if any values have changed from initial state
const hasChanges = computed(() => {
  // Basic field comparisons
  const basicFieldsChanged
    = inputText.value !== initialValues.value.inputText
      || model.value?.value !== initialValues.value.model?.value
      || selectedVoice.value?.id !== initialValues.value.selectedVoice?.id
      || selectedEmotion.value?.emotion_key
      !== initialValues.value.selectedEmotion?.emotion_key
      || speed.value !== initialValues.value.speed
      || outputFormat.value !== initialValues.value.outputFormat
      || outputChannel.value !== initialValues.value.outputChannel

  // File comparison with better performance
  const filesChanged = compareFileArrays(
    selectedFiles.value,
    initialValues.value.selectedFiles
  )

  return basicFieldsChanged || filesChanged
})

const generateModeActive = computed({
  get() {
    return (route.query.mode as string) || 'text-to-speech'
  },
  set(mode) {
    // Hash is specified here to prevent the page from scrolling to the top
    router.push({
      path: '/app/speech-gen',
      query: {
        ...route.query,
        mode
      }
    })
  }
})

// Helper function to perform the actual generation
const performGeneration = async () => {
  let result
  if (generateModeActive.value === 'document-to-speech') {
    result = await handleDocumentToSpeech()
  } else {
    result = await handleTextToSpeech()
  }

  if (result) {
    // Determine wait time based on mode and text length
    let waitTime = '30'
    let unit = t('seconds')
    if (generateModeActive.value === 'document-to-speech') {
      waitTime = '' // No specific time for document
    } else if (inputText.value && inputText.value.length > 1000) {
      waitTime = '3'
      unit = t('minutes')
    }

    const description = generateModeActive.value === 'document-to-speech'
      ? t('We are starting to generate speech from your document, please check back later...')
      : t('We are starting to generate your speech, please wait about {time} {unit}...', { time: waitTime, unit })

    toast.add({
      id: 'success',
      title: 'Speech Generation',
      description,
      color: 'success'
    })

    // Update initial values after successful generation
    initialValues.value = {
      inputText: inputText.value,
      model: model.value,
      selectedVoice: selectedVoice.value,
      selectedEmotion: selectedEmotion.value,
      speed: speed.value,
      outputFormat: outputFormat.value,
      outputChannel: outputChannel.value,
      selectedFiles: [...selectedFiles.value]
    }
  }
}

const onGenerate = async () => {
  if (!isAuthenticated.value) {
    router.push('/auth/login')
    return
  }

  // Define validation rules
  const validationRules = [
    () => ({
      isValid: hasSelectedFiles.value || !!inputText.value?.trim(),
      message: t('Please enter text or select a file to generate speech.')
    }),
    () => ({
      isValid: hasSelectedFiles.value || !inputText.value?.trim() || inputText.value.trim().length >= 4,
      message: t('Text must be at least 4 characters long.')
    }),
    () => ({
      isValid: hasSelectedFiles.value || !inputText.value?.trim() || inputText.value.trim().length <= maxTextLength.value,
      message: t('Text must not exceed {maxLength} characters.', { maxLength: maxTextLength.value })
    }),
    commonValidationRules.requiredValue(
      selectedVoice.value,
      t('Please select a voice for speech generation.')
    )
  ]

  // Use the unified generation confirmation logic
  await handleGeneration({
    generationType: 'speech',
    hasChanges,
    hasResult: computed(() => !!textToSpeechResult.value),
    onGenerate: performGeneration,
    validationRules
  })
}

const handleTextToSpeech = async () => {
  return await textToSpeechStore.textToSpeech({
    input: inputText.value,
    model: model.value.value,
    voices: [
      {
        name: selectedVoice.value?.speaker_name,
        voice: {
          id: selectedVoice.value?.id,
          name: selectedVoice.value?.speaker_name
        }
      }
    ],
    emotion: selectedEmotion.value?.emotion_key,
    speed: speed.value,
    output_format: outputFormat.value,
    custom_prompt: custom_prompt.value
  })
}

const handleDocumentToSpeech = async () => {
  for (const file of selectedFiles.value) {
    return await textToSpeechStore.documentToSpeech(file, {
      input: inputText.value,
      model: model.value.value,
      speed: speed.value,
      output_format: outputFormat.value,
      voices: [
        {
          name: selectedVoice.value?.speaker_name,
          voice: {
            id: selectedVoice.value?.id,
            name: selectedVoice.value?.speaker_name
          }
        }
      ]
      // custom_prompt: custom_prompt.value
    })
  }
}

const handleFilesSelected = (files: File[]) => {
  textToSpeechStore.selectedFiles = files
}

const generateModes = computed(() => {
  return [
    {
      label: t('Text to Speech'),
      icon: 'ion:language-sharp',
      value: 'text-to-speech'
    },
    {
      label: t('Document to Speech'),
      icon: 'basil:document-outline',
      value: 'document-to-speech'
    }
  ]
})

const onSelectAnotherVoice = () => {
  textToSpeechStore.textToSpeechResult = null
}

const outputFormatItems = computed(() => {
  return outputFormats.map(format => ({
    label: format.label,
    value: format.value
  }))
})
</script>

<template>
  <UContainer class="mt-0">
    <div
      class="grid grid-cols-1 lg:grid-cols-2 sm:gap-4 lg:gap-6 space-y-8 sm:space-y-0"
    >
      <UCard>
        <div class="flex flex-col gap-6">
          <UTabs
            v-model="generateModeActive"
            :items="generateModes"
            class="w-full"
            color="neutral"
            :content="false"
            size="sm"
          />
          <div
            class="grid grid-cols-1 md:grid-cols-2 gap-6"
            :class="{
              'md:grid-cols-3': generateModeActive === 'text-to-speech'
            }"
          >
            <UFormField :label="$t('model')">
              <BaseModelSelect
                v-model="model"
                :models="models"
                class="w-full"
              />
            </UFormField>
            <UFormField
              v-if="
                model?.options?.includes('emotion')
                  && generateModeActive === 'text-to-speech'
              "
              :label="$t('emotion')"
            >
              <BaseSpeechEmotionSelectModal
                v-model="selectedEmotion"
                size="sm"
              />
            </UFormField>
            <UFormField
              v-if="generateModeActive === 'text-to-speech'"
              :label="$t('Custom Prompt')"
            >
              <BaseCustomPromptSelect v-model="custom_prompt" />
            </UFormField>
          </div>
          <UFormField
            v-if="generateModeActive === 'text-to-speech'"
            :label="$t('Text')"
          >
            <template #hint>
              <div class="text-xs">
                {{ formatNumber(inputTextLength) }} / {{ formatNumber(maxTextLength) }}
              </div>
            </template>
            <UTextarea
              v-model="inputText"
              class="w-full"
              :placeholder="
                hasSelectedFiles
                  ? $t('Generate speech from selected file')
                  : $t(
                    'Start writing or paste your text here to generate speech...'
                  )
              "
              :rows="11"
              :maxlength="maxTextLength"
            />
          </UFormField>
          <UFormField
            :label="$t('Voice')"
            class="w-full lg:hidden"
          >
            <UDrawer direction="right">
              <UButton
                :label="
                  selectedVoice ? selectedVoice.speaker_name : $t('selectVoice')
                "
                :icon="
                  selectedVoice
                    ? 'fluent:person-voice-16-regular'
                    : 'lucide:users'
                "
                color="neutral"
                variant="outline"
                trailing-icon="lucide:chevron-down"
                class="w-full"
                size="sm"
                :ui="{
                  trailingIcon: 'ml-auto'
                }"
              />

              <template #content>
                <VoicesLibraries full-screen />
              </template>
            </UDrawer>
          </UFormField>

          <UFormField
            v-if="generateModeActive === 'document-to-speech'"
            class="flex flex-col gap-0"
            :label="$t('Upload Document')"
          >
            <BaseFiles
              v-if="hasSelectedFiles"
              v-model="selectedFiles"
              :upload-progress="uploadProgress"
              class="w-full"
            />
            <BaseFileSelect
              v-else
              v-model="selectedFiles"
              :support-files="supportFiles"
              :support-files-display="supportFilesDisplay"
              @update:model-value="handleFilesSelected"
            />
          </UFormField>
          <div class="flex flex-row gap-6">
            <!-- Speed Settings -->
            <UFormField
              v-if="model?.options?.includes('speed')"
              :label="$t('speed')"
              class="flex-1"
            >
              <div class="flex flex-col gap-3">
                <UInputNumber
                  v-model="speed"
                  :min="speedConfig.min"
                  :max="speedConfig.max"
                  :step="speedConfig.step"
                  size="sm"
                  class="w-full"
                />
                <USlider
                  v-model="speed"
                  :min="speedConfig.min"
                  :max="speedConfig.max"
                  :step="speedConfig.step"
                  class="w-full"
                />
                <div class="flex justify-between text-xs text-gray-400">
                  <span>{{ speedConfig.min }}x</span>
                  <span>{{ speedConfig.max }}x</span>
                </div>
              </div>
            </UFormField>

            <!-- Output Format Settings -->
            <UFormField
              v-if="model?.options?.includes('outputFormat')"
              :label="$t('outputFormat')"
            >
              <URadioGroup
                v-model="outputFormat"
                orientation="vertical"
                variant="card"
                value-key="value"
                :items="outputFormatItems"
                size="xs"
              />
            </UFormField>
          </div>

          <!-- Output Channel Settings -->
          <!-- <UFormField
            v-if="model?.options?.includes('outputChannel')"
            :label="$t('outputChannel')"
          >
            <USelectMenu
              v-model="outputChannel"
              :items="outputChannels"
              :placeholder="$t('selectChannel')"
              :search-input="false"
              value-key="value"
              size="sm"
            />
          </UFormField> -->

          <div class="flex justify-end gap-2 items-center flex-row">
            <div
              v-if="!runtimeConfig.public.features.beta"
              class="text-xs text-right"
            >
              <div>
                {{
                  $t("Credits: {credits} remaining", {
                    credits: formatNumber(user_credit?.available_credit || 0)
                  })
                }}
              </div>
              <div class="text-primary">
                {{
                  $t("Price per 1 character: {cost} Credits", {
                    cost:
                      getServicePriceByModelName(model?.value)
                        ?.effective_price || 0
                  })
                }}
              </div>
            </div>
            <UButton
              color="primary"
              :label="$t('Generate Speech')"
              class="bg-gradient-to-r from-primary-500 to-violet-500 max-h-10 dark:text-white hover:from-primary-600 hover:to-violet-600 !cursor-pointer"
              trailing-icon="mingcute:ai-fill"
              :loading="loadings['textToSpeech']"
              :disabled="!inputText && !hasSelectedFiles"
              @click="authorize(onGenerate)"
            />
          </div>
        </div>
      </UCard>
      <Motion
        v-if="
          (textToSpeechResult || loadings['textToSpeech'])
            && !errors['textToSpeech']
        "
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.6,
          delay: 0.5
        }"
      >
        <AIToolSpeechCard
          ref="aiToolSpeechCardRef"
          v-bind="textToSpeechResult"
          :data="textToSpeechResult"
          :loading="loadings['textToSpeech']"
          class="h-full"
          @select-another-voice="onSelectAnotherVoice"
        />
      </Motion>
      <VoicesLibraries
        v-else
        class="hidden lg:block h-fit sticky top-20"
      />
      <UCard
        v-if="false"
        :ui="{
          body: 'h-full dark:text-muted/40'
        }"
      >
        <div class="flex flex-col items-center justify-center h-full">
          <div>
            <UIcon
              :name="
                errors['textToSpeech']
                  ? 'i-lucide-alert-circle'
                  : 'i-lucide-mic'
              "
              class="text-6xl mb-2"
              :class="errors['textToSpeech'] ? 'text-error' : ''"
            />
          </div>
          <div
            v-if="errors['textToSpeech']"
            class="text-sm text-error"
          >
            {{ errors["textToSpeech"] }}
          </div>
          <div
            v-else
            class="text-sm"
          >
            {{ $t("Your generated speech will appear here") }}
          </div>
        </div>
      </UCard>
    </div>
  </UContainer>
</template>
