<script setup lang="ts">
import { formatNumber } from '~/utils'
import {
  compareImageArrays,
  commonValidationRules
} from '~/utils/generationValidation'

interface ImageFile {
  src: string
  alt: string
  file: File
}
const { authorize } = useAuthorize()
const authStore = useAuthStore()
const { isAuthenticated, user_credit } = storeToRefs(authStore)
const { model, models } = useVideoGenModels()
const {
  duration,
  durationOptions,
  isDurationSelectable,
  enhancePrompt,
  isEnhancePromptLocked
} = useVideoGenOptions()
const { videoDimension } = useVideoDimensions()
const { resolution, isResolutionSelectable } = useVideoResolution()
const videoStyle = ref('Cinematic')
const router = useRouter()
const toast = useToast()
const { handleGeneration } = useGenerationConfirmation()
const { t } = useI18n()
const runtimeConfig = useRuntimeConfig()
const textToVideoStore = useTextToVideoStore()
const {
  textToVideoResult,
  aiToolVideoCardRef,
  prompt,
  negativePrompt,
  loadings,
  errors
} = storeToRefs(textToVideoStore)
const productStore = useProductStore()
const { getServicePriceByModelName } = storeToRefs(productStore)

// Calculate actual cost based on duration
const actualCost = computed(() => {
  const pricePerSecond
    = getServicePriceByModelName.value(model.value?.value)?.effective_price || 0
  return pricePerSecond * duration.value
})
// Local state for selected images
const selectedImages = ref<ImageFile[]>([])

// Store initial values to compare for changes
const initialValues = ref({
  prompt: '',
  negativePrompt: '',
  model: models[0],
  videoDimension: '16:9',
  videoStyle: 'Cinematic',
  enhancePrompt: false,
  resolution: '720p',
  selectedImages: [] as ImageFile[]
})

// Initialize initial values on mount
onMounted(() => {
  initialValues.value = {
    prompt: prompt.value,
    negativePrompt: negativePrompt.value,
    model: model.value,
    videoDimension: videoDimension.value,
    videoStyle: videoStyle.value,
    enhancePrompt: enhancePrompt.value,
    resolution: resolution.value,
    selectedImages: [...selectedImages.value]
  }
})

// Check if any values have changed from initial state
const hasChanges = computed(() => {
  // Basic field comparisons
  const basicFieldsChanged
    = prompt.value !== initialValues.value.prompt
      || negativePrompt.value !== initialValues.value.negativePrompt
      || model.value?.value !== initialValues.value.model?.value
      || videoDimension.value !== initialValues.value.videoDimension
      || videoStyle.value !== initialValues.value.videoStyle
      || enhancePrompt.value !== initialValues.value.enhancePrompt
      || resolution.value !== initialValues.value.resolution

  // Image comparison with better performance
  const imagesChanged = compareImageArrays(
    selectedImages.value,
    initialValues.value.selectedImages
  )

  return basicFieldsChanged || imagesChanged
})

// Handle image selection
const handleImagesSelected = (images: ImageFile[]) => {
  selectedImages.value = images
  // Also update store for backward compatibility
  textToVideoStore.selectedImages = images
}

// Helper function to perform the actual generation
const performGeneration = async () => {
  // Extract File objects from selected images
  const files = selectedImages.value.map(img => img.file).filter(Boolean)

  const result = await textToVideoStore.textToVideo({
    prompt: prompt.value,
    negative_prompt: negativePrompt.value,
    model: model.value?.value || 'veo-2',
    aspect_ratio: videoDimension.value || '16:9',
    enhance_prompt: enhancePrompt.value,
    duration: duration.value,
    resolution: resolution.value,
    files: files
  })

  if (result) {
    const generationType
      = selectedImages.value.length > 0 ? t('videoGen.imageToVideo') : t('videoGen.textToVideo')
    const generationMessage = selectedImages.value.length > 0
      ? t('videoGen.imageToVideoGenerationStarted')
      : t('videoGen.textToVideoGenerationStarted')

    toast.add({
      id: 'success',
      title: `${generationType} ${t('videoGen.generated')}`,
      description: generationMessage,
      color: 'success'
    })

    // Update initial values after successful generation
    initialValues.value = {
      prompt: prompt.value,
      negativePrompt: negativePrompt.value,
      model: model.value,
      videoDimension: videoDimension.value,
      videoStyle: videoStyle.value,
      enhancePrompt: enhancePrompt.value,
      resolution: resolution.value,
      selectedImages: [...selectedImages.value]
    }
  }
}

const onGenerate = async () => {
  if (!isAuthenticated.value) {
    router.push('/auth/login')
    return
  }

  // Define validation rules
  const validationRules = [
    commonValidationRules.requiredText(
      prompt.value,
      t('videoGen.pleaseEnterPrompt')
    )
  ]

  // Use the unified generation confirmation logic
  await handleGeneration({
    generationType: 'video',
    hasChanges,
    hasResult: computed(() => !!textToVideoResult.value),
    onGenerate: performGeneration,
    validationRules
  })
}

const onUsePrompt = (newPrompt: string) => {
  prompt.value = newPrompt
  // scroll to top and focus on prompt input
  nextTick(() => {
    // scroll to top smoothly
    window.scrollTo({ top: 0, behavior: 'smooth' })

    // try to focus the prompt input after scrolling
    setTimeout(() => {
      // look for the prompt input (UChatPrompt component)
      const promptInput = document.querySelector(
        '[class*="chat-prompt"] textarea, [class*="chat-prompt"] input'
      )
      if (promptInput && promptInput instanceof HTMLElement) {
        promptInput.focus()
      }
    }, 500)
  })
}

const addNegativePromptSuggestion = (suggestion: string) => {
  if (negativePrompt.value) {
    // If there's already content, add a comma and space before the new suggestion
    negativePrompt.value += ', ' + suggestion
  } else {
    // If empty, just add the suggestion
    negativePrompt.value = suggestion
  }
}

const enhancePromptItems = computed(() => {
  const items = [
    {
      label: t('On'),
      value: 'true',
      description: isEnhancePromptLocked.value
        ? t('videoGen.enhancePromptOnRequired')
        : t('videoGen.enhancePromptOn'),
      disabled: isEnhancePromptLocked.value && !enhancePrompt.value
    },
    {
      label: t('Off'),
      value: 'false',
      description: t('videoGen.enhancePromptOff'),
      disabled: isEnhancePromptLocked.value && enhancePrompt.value
    }
  ]

  return items
})

// Convert enhancePrompt to string for radio group
const enhancePromptString = computed({
  get: () => enhancePrompt.value.toString(),
  set: (value: string) => {
    enhancePrompt.value = value === 'true'
  }
})
</script>

<template>
  <UContainer class="mt-0">
    <div
      class="grid grid-cols-1 lg:grid-cols-2 sm:gap-4 lg:gap-6 space-y-8 sm:space-y-0"
    >
      <UCard>
        <div class="flex flex-col gap-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UFormField :label="$t('model')">
              <BaseModelSelect
                v-model="model"
                :models="models"
                class="w-full"
              />
            </UFormField>
            <div
              v-if="model?.options?.includes('yourImage')"
              class="flex flex-row gap-3 items-end"
            >
              <UFormField :label="$t('videoGen.imageReference')">
                <BaseImageSelect
                  v-model="selectedImages"
                  :multiple="true"
                  @update:model-value="handleImagesSelected"
                />
              </UFormField>
              <BaseImageSelectedList
                v-model="selectedImages"
                @update:model-value="handleImagesSelected"
              />
            </div>
          </div>
          <UFormField :label="$t('Prompt')">
            <UTextarea
              v-model="prompt"
              class="w-full"
              :placeholder="$t('Describe the video you want to generate...')"
              :rows="6"
            />
          </UFormField>

          <div>
            <UFormField :label="$t('videoGen.negativePrompt')">
              <template #hint>
                <UTooltip
                  :delay-duration="0"
                  :text="$t('videoGen.negativePromptTooltip')"
                >
                  <UIcon name="material-symbols:help" />
                </UTooltip>
              </template>
              <UTextarea
                v-model="negativePrompt"
                class="w-full"
                :placeholder="$t('videoGen.negativePromptPlaceholder')"
                :rows="3"
              />
              <template #description>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ $t("videoGen.negativePromptDescription") }}
                </span>
                <!-- Negative Prompt Suggestions -->
                <div class="space-y-2 mt-0">
                  <div class="text-xs font-medium text-gray-600 dark:text-gray-300">
                    {{ $t("videoGen.negativePromptSuggestions") }}:
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <UButton
                      v-for="i in 6"
                      :key="i"
                      size="xs"
                      variant="outline"
                      color="neutral"
                      :label="$t(`videoGen.negativePromptSuggestion${i}`)"
                      @click="
                        addNegativePromptSuggestion(
                          $t(`videoGen.negativePromptSuggestion${i}`)
                        )
                      "
                    />
                  </div>
                </div>
              </template>
            </UFormField>
          </div>
          <div class="space-y-6">
            <!-- First row: Enhance Prompt and Aspect Ratio -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UFormField :label="$t('videoGen.enhancePrompt')">
                <template #hint>
                  <UTooltip
                    :delay-duration="0"
                    :text="
                      isEnhancePromptLocked
                        ? $t('videoGen.enhancePromptLocked')
                        : enhancePrompt
                          ? $t('videoGen.enhancePromptOn')
                          : $t('videoGen.enhancePromptNotRefined')
                    "
                  >
                    <UIcon name="material-symbols:help" />
                  </UTooltip>
                </template>
                <URadioGroup
                  v-model="enhancePromptString"
                  orientation="horizontal"
                  variant="card"
                  value-key="value"
                  :items="enhancePromptItems"
                  size="xs"
                  :disabled="isEnhancePromptLocked"
                />
              </UFormField>

              <UFormField :label="$t('videoGen.aspectRatio')">
                <BaseVideoDimensionsSelect :options="model?.ratios" />
              </UFormField>
            </div>

            <!-- Second row: Duration and Resolution -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UFormField
                v-if="isDurationSelectable"
                :label="$t('videoGen.duration')"
              >
                <template #hint>
                  <UTooltip
                    :delay-duration="0"
                    :text="$t('videoGen.selectDuration')"
                  >
                    <UIcon name="material-symbols:help" />
                  </UTooltip>
                </template>
                <URadioGroup
                  v-model="duration"
                  orientation="horizontal"
                  variant="card"
                  value-key="value"
                  :items="durationOptions"
                  size="xs"
                />
              </UFormField>

              <UFormField
                v-if="isResolutionSelectable"
                :label="$t('videoGen.resolution')"
              >
                <template #hint>
                  <UTooltip
                    :delay-duration="0"
                    :text="$t('videoGen.selectResolution')"
                  >
                    <UIcon name="material-symbols:help" />
                  </UTooltip>
                </template>
                <BaseVideoResolutionSelect class="" />
              </UFormField>
            </div>
          </div>

          <div class="flex justify-end gap-2 items-center flex-row">
            <div
              v-if="!runtimeConfig.public.features.beta"
              class="text-xs text-right"
            >
              <div>
                {{
                  $t("videoGen.creditsRemaining", {
                    credits: formatNumber(user_credit?.available_credit || 0)
                  })
                }}
              </div>
              <div class="text-primary">
                {{
                  $t("videoGen.generationCost", {
                    cost: actualCost,
                    duration: duration
                  })
                }}
              </div>
            </div>
            <UButton
              color="primary"
              :label="$t('videoGen.generateVideo')"
              class="bg-gradient-to-r from-primary-500 to-primary-500 max-h-10 dark:text-white hover:from-primary-600 hover:to-success-600 !cursor-pointer"
              trailing-icon="line-md:arrow-right"
              :loading="loadings['textToVideo']"
              :disabled="!prompt"
              @click="authorize(onGenerate)"
            />
          </div>
        </div>
      </UCard>
      <Motion
        v-if="
          (textToVideoResult || loadings['textToVideo'])
            && !errors['textToVideo']
        "
        ref="aiToolVideoCardRef"
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.6,
          delay: 0.5
        }"
      >
        <AIToolVideoCard
          v-bind="textToVideoResult"
          :data="textToVideoResult"
          :loading="loadings['textToVideo']"
          class="h-full"
        />
      </Motion>
      <UCard
        v-else
        :ui="{
          body: 'h-full dark:text-muted/40'
        }"
      >
        <div
          v-if="errors['textToVideo']"
          class="flex flex-col items-center justify-center h-full"
        >
          <div>
            <UIcon
              name="i-lucide-alert-circle"
              class="text-6xl mb-2 text-error"
            />
          </div>
          <div class="text-sm text-error">
            {{ $t(errors["textToVideo"] || "videoGen.somethingWentWrong") }}
          </div>
        </div>
        <div
          v-else
          class="h-full"
        >
          <div class="mb-4 text-center">
            <h3
              class="text-lg font-semibold text-gray-900 dark:text-white mb-2"
            >
              {{ $t("videoGen.examplesTitle") }}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ $t("videoGen.examplesDescription") }}
            </p>
          </div>
          <BaseVideoComparisonDemo :height="'350px'" />
        </div>
      </UCard>
    </div>
    <!-- Video Prompt Gallery -->
    <Motion
      :initial="{
        scale: 1.1,
        opacity: 0,
        filter: 'blur(20px)'
      }"
      :animate="{
        scale: 1,
        opacity: 1,
        filter: 'blur(0px)'
      }"
      :transition="{
        duration: 0.6,
        delay: 1.2
      }"
    >
      <VideoPromptGallery
        class="mt-8"
        @use-prompt="onUsePrompt"
      />
    </Motion>
  </UContainer>
</template>
