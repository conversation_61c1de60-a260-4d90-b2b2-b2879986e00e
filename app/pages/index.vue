<script setup lang="ts">
// Landing page for AI video generation platform

// Set layout to fullscreen for immersive experience
import { en, vi, ja, zh_cn, es, fr, de, pt } from '@nuxt/ui/locale'
import { storeToRefs } from 'pinia'
import { watch } from 'vue'

// SEO meta tags for landing page
useSeoMeta({
  title: 'GeminiGen AI - Multi Modal AI Content Generation',
  description: 'Multi Modal AI Content Generation'
})

definePageMeta({
  transparentHeader: true
})
const nuxtApp = useNuxtApp()
const { activeHeadings, updateHeadings } = useScrollspy()
const { t } = useI18n()
const appStore = useAppStore()
const { lastMenus, loading, locale, localeForI18n } = storeToRefs(appStore)
const router = useRouter()
const route = useRoute()
const runtimeConfig = useRuntimeConfig()

// Language and locale handling
const { locale: i18nLocale, setLocale } = useI18n()
watch(i18nLocale, (newLocale: string) => {
  // zh-CN -> zh_cn
  locale.value = newLocale
  setLocale(localeForI18n.value)
})

const items = computed(() => {
  const baseItems = [
    {
      label: t('Home'),
      onClick: () => {
        router.push({
          name: lastMenus.value.app
        })
      },
      active: route.name?.toString().includes('app'),
      showsOn: ['header', 'drawer']
    },
    {
      label: t('History'),
      to: '/history',
      showsOn: ['header', 'drawer']
    },
    {
      slot: 'api-docs',
      label: t('API'),
      showsOn: ['header', 'drawer'],
      onClick: () => {
        // Disable API navigation when beta feature is enabled
        if (runtimeConfig.public.features.beta) {
          return
        }
        navigateTo('https://docs.geminigen.ai/', {
          external: true,
          open: {
            target: '_blank'
          }
        })
      },
      target: '_blank'
    },
    {
      label: t('footer.privacy'),
      to: '/privacy',
      showsOn: ['drawer']
    },
    {
      label: t('footer.terms'),
      to: '/terms',
      showsOn: ['drawer']
    }
  ]

  // Add pricing menu only if beta feature is disabled
  if (!runtimeConfig.public.features.beta) {
    baseItems.splice(2, 0, {
      label: t('Pricing'),
      to: '/pricing',
      showsOn: ['header', 'drawer']
    })
  }

  return baseItems
})

nuxtApp.hooks.hookOnce('page:finish', () => {
  updateHeadings(
    [
      document.querySelector('#features'),
      document.querySelector('#pricing'),
      document.querySelector('#testimonials')
    ].filter(Boolean) as Element[]
  )
})

const authStore = useAuthStore()
const { firstLoad, isAuthenticated } = storeToRefs(authStore)
onMounted(() => {
  if (route.query.hard === 'true') {
    return
  }
  if (isAuthenticated.value) {
    // redirect to /app
    navigateTo('/app')
  }
})
</script>

<template>
  <div class="min-h-screen">
    <!-- Hero Section -->
    <LandingHeroSection class="-mt-16" />
    {{ firstLoad }}

    <!-- Features Section -->
    <section id="features">
      <LandingFeaturesSection />
    </section>

    <!-- How to Create Section -->
    <LandingHowToSection />

    <!-- FAQ Section -->
    <section id="faq">
      <LandingFAQSection />
    </section>

    <!-- Footer -->
    <!-- <LandingFooter /> -->
  </div>
</template>

<style scoped>
/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Navigation backdrop blur effect */
nav {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Ensure sections have proper spacing for fixed nav */
section {
  scroll-margin-top: 80px;
}
</style>
