<script setup lang="ts">
const { data: page } = await useAsyncData('about', () => {
  return queryCollection('about').first()
})
if (!page.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Page not found',
    fatal: true
  })
}

const { global } = useAppConfig()

// Enhanced SEO meta tags for about page
useSeoMeta({
  title: page.value?.seo?.title || page.value?.title || 'About GeminiGen AI - AI Content Generation Platform',
  ogTitle: page.value?.seo?.title || page.value?.title || 'About GeminiGen AI - Advanced AI Technology',
  description: page.value?.seo?.description || page.value?.description || 'Learn about GeminiGen AI, the leading platform for AI-powered content generation including images, videos, and speech synthesis.',
  ogDescription: page.value?.seo?.description || page.value?.description || 'Discover the story behind GeminiGen AI and our mission to democratize AI content creation.',
  keywords: 'about GeminiGen AI, AI company, content generation technology, artificial intelligence platform, team'
})

// Add structured data for about page
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'AboutPage',
        'name': 'About GeminiGen AI',
        'description': 'Learn about GeminiGen AI and our AI content generation platform',
        'mainEntity': {
          '@type': 'Organization',
          'name': 'GeminiGen AI',
          'description': 'AI-powered content generation platform',
          'foundingDate': '2024',
          'sameAs': [
            'https://discord.gg/vJnYe86T8F'
          ]
        }
      })
    }
  ]
})
</script>

<template>
  <UPage v-if="page">
    <UPageHero
      :title="page.title"
      :description="page.description"
      orientation="horizontal"
      :ui="{
        container: 'lg:flex sm:flex-row items-center',
        title: '!mx-0 text-left',
        description: '!mx-0 text-left',
        links: 'justify-start'
      }"
    >
      <UColorModeAvatar
        class="sm:rotate-4 size-36 rounded-lg ring ring-default ring-offset-3 ring-offset-(--ui-bg)"
        :light="global.picture?.light!"
        :dark="global.picture?.dark!"
        :alt="global.picture?.alt!"
      />
    </UPageHero>
    <UPageSection
      :ui="{
        container: '!pt-0'
      }"
    >
      <MDC
        :value="page.content"
        unwrap="p"
      />
      <div class="flex flex-row justify-center items-center py-10 space-x-[-2rem]">
        <PolaroidItem
          v-for="(image, index) in page.images"
          :key="index"
          :image="image"
          :index
        />
      </div>
    </UPageSection>
  </UPage>
</template>
